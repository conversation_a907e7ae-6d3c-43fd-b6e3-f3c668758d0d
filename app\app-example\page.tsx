import AppLayout from '@/components/layouts/AppLayout';
import SideMenuTemplate from '@/components/layouts/SideMenuTemplate';

export default function AppExample() {
  return (
    <AppLayout sideMenuContent={<SideMenuTemplate />}>
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            App Layout with Side Menu
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            This is the main content area. The side menu is responsive and works on all devices.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900 mb-3">Features</h2>
              <ul className="space-y-2 text-gray-600">
                <li>• Responsive side menu</li>
                <li>• Mobile hamburger menu</li>
                <li>• Smooth animations</li>
                <li>• Customizable content</li>
              </ul>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900 mb-3">Layout</h2>
              <ul className="space-y-2 text-gray-600">
                <li>• Fixed side menu on desktop</li>
                <li>• Overlay menu on mobile</li>
                <li>• Scrollable content area</li>
                <li>• Full height layout</li>
              </ul>
            </div>
          </div>

          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <p className="text-blue-800">
              <strong>Try it:</strong> Resize your browser window to see the responsive behavior, 
              or use mobile view to test the hamburger menu.
            </p>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
