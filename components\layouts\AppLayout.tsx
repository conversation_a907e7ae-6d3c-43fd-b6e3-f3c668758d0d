'use client';

import { useState } from 'react';
import SideMenu, { MobileMenuButton } from './SideMenu';

interface AppLayoutProps {
  children: React.ReactNode;
  sideMenuContent: React.ReactNode;
}

export default function AppLayout({ children, sideMenuContent }: AppLayoutProps) {
  const [isSideMenuOpen, setIsSideMenuOpen] = useState(false);

  const toggleSideMenu = () => {
    setIsSideMenuOpen(!isSideMenuOpen);
  };

  return (
    <div className="h-screen w-screen flex overflow-hidden bg-gray-50">
      {/* Side Menu */}
      <SideMenu isOpen={isSideMenuOpen} onToggle={toggleSideMenu}>
        {sideMenuContent}
      </SideMenu>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Mobile Header */}
        <header className="lg:hidden bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
          <MobileMenuButton onClick={toggleSideMenu} />
          <h1 className="text-lg font-semibold text-gray-900">App</h1>
          <div className="w-10" /> {/* Spacer for centering */}
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="h-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
